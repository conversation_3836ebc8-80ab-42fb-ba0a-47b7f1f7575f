# Generated by Django 5.2.3 on 2025-06-27 10:56

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BookBase',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('call_number', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('material', models.Char<PERSON><PERSON>(max_length=50)),
                ('audience', models.Char<PERSON>ield(max_length=50)),
                ('isbn', models.Char<PERSON>ield(max_length=13)),
                ('year_of_publication', models.PositiveBigIntegerField()),
                ('publisher', models.Char<PERSON><PERSON>(max_length=100)),
                ('overview', models.TextField()),
                ('category', models.Char<PERSON>ield(max_length=50)),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='cover_images/')),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
