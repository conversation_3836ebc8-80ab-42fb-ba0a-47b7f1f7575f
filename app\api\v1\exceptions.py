from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import IntegrityError
from django.http import Http404
import logging

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Log the exception
    logger.error(f"Exception in {context.get('view', 'Unknown view')}: {str(exc)}")
    
    if response is not None:
        # DRF handled the exception, format the response
        custom_response_data = format_error_response(response.data, response.status_code)
        response.data = custom_response_data
        return response
    
    # Handle Django validation errors
    if isinstance(exc, DjangoValidationError):
        if hasattr(exc, 'message_dict'):
            # Field-specific validation errors
            errors = {}
            for field, messages in exc.message_dict.items():
                errors[field] = messages if isinstance(messages, list) else [messages]
        else:
            # Non-field validation errors
            errors = {'non_field_errors': exc.messages if hasattr(exc, 'messages') else [str(exc)]}
        
        return Response(
            format_error_response(errors, status.HTTP_400_BAD_REQUEST),
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Handle database integrity errors
    if isinstance(exc, IntegrityError):
        error_message = "A database constraint was violated."
        if "UNIQUE constraint failed" in str(exc):
            error_message = "A record with this information already exists."
        elif "NOT NULL constraint failed" in str(exc):
            error_message = "Required field is missing."
        
        return Response(
            format_error_response({'database_error': [error_message]}, status.HTTP_400_BAD_REQUEST),
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Handle 404 errors
    if isinstance(exc, Http404):
        return Response(
            format_error_response({'not_found': ['The requested resource was not found.']}, status.HTTP_404_NOT_FOUND),
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Handle unexpected errors
    logger.critical(f"Unhandled exception: {str(exc)}", exc_info=True)
    return Response(
        format_error_response(
            {'server_error': ['An unexpected error occurred. Please try again later.']},
            status.HTTP_500_INTERNAL_SERVER_ERROR
        ),
        status=status.HTTP_500_INTERNAL_SERVER_ERROR
    )


def format_error_response(errors, status_code):
    """
    Format error response in a consistent structure
    """
    if isinstance(errors, dict):
        # Field-specific errors
        formatted_errors = {}
        for field, messages in errors.items():
            if isinstance(messages, list):
                formatted_errors[field] = messages
            elif isinstance(messages, dict):
                # Nested errors (e.g., from nested serializers)
                formatted_errors[field] = messages
            else:
                formatted_errors[field] = [str(messages)]
    elif isinstance(errors, list):
        # Non-field errors
        formatted_errors = {'non_field_errors': errors}
    else:
        # Single error message
        formatted_errors = {'non_field_errors': [str(errors)]}
    
    return {
        'success': False,
        'status_code': status_code,
        'errors': formatted_errors,
        'message': get_error_message(formatted_errors, status_code)
    }


def get_error_message(errors, status_code):
    """
    Generate a user-friendly error message based on the errors
    """
    if status_code == status.HTTP_400_BAD_REQUEST:
        return "Validation failed. Please check the provided data."
    elif status_code == status.HTTP_401_UNAUTHORIZED:
        return "Authentication required."
    elif status_code == status.HTTP_403_FORBIDDEN:
        return "You don't have permission to perform this action."
    elif status_code == status.HTTP_404_NOT_FOUND:
        return "The requested resource was not found."
    elif status_code == status.HTTP_405_METHOD_NOT_ALLOWED:
        return "This method is not allowed for this endpoint."
    elif status_code == status.HTTP_409_CONFLICT:
        return "A conflict occurred with the current state of the resource."
    elif status_code >= 500:
        return "An internal server error occurred. Please try again later."
    else:
        return "An error occurred while processing your request."


def format_success_response(data, status_code=status.HTTP_200_OK, message=None):
    """
    Format success response in a consistent structure
    """
    if message is None:
        if status_code == status.HTTP_201_CREATED:
            message = "Resource created successfully."
        elif status_code == status.HTTP_200_OK:
            message = "Request processed successfully."
        elif status_code == status.HTTP_204_NO_CONTENT:
            message = "Request processed successfully."
        else:
            message = "Request completed successfully."
    
    response_data = {
        'success': True,
        'status_code': status_code,
        'message': message,
        'data': data
    }
    
    return response_data


class BookValidationError(Exception):
    """Custom exception for book validation errors"""
    def __init__(self, message, field=None):
        self.message = message
        self.field = field
        super().__init__(self.message)


class BookNotFoundError(Exception):
    """Custom exception for when a book is not found"""
    def __init__(self, message="Book not found"):
        self.message = message
        super().__init__(self.message)


class DuplicateBookError(Exception):
    """Custom exception for duplicate book creation"""
    def __init__(self, message="A book with this ISBN already exists"):
        self.message = message
        super().__init__(self.message)
