from django.db import models
import uuid
from django.core.validators import MinLengthValidator
from django.core.exceptions import ValidationError
import re


def validate_isbn(value):
    """Validate ISBN-10 or ISBN-13 format"""
    # Remove any hyphens or spaces
    isbn = re.sub(r'[-\s]', '', value)

    # Check if it's all digits
    if not isbn.isdigit():
        raise ValidationError('ISBN must contain only digits (and optional hyphens/spaces)')

    # Check length
    if len(isbn) not in [10, 13]:
        raise ValidationError('ISBN must be either 10 or 13 digits long')

    return isbn


def validate_year(value):
    """Validate publication year"""
    current_year = 2025  # You might want to use datetime.now().year
    if value < 1000 or value > current_year:
        raise ValidationError(f'Year must be between 1000 and {current_year}')


class BookBase(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    call_number = models.CharField(
        max_length=50,
        validators=[MinLengthValidator(1)],
        help_text="Library call number for the book"
    )
    material = models.CharField(
        max_length=50,
        validators=[MinLengthValidator(1)],
        help_text="Type of material (e.g., book, journal, etc.)"
    )
    audience = models.CharField(
        max_length=50,
        validators=[MinLengthValidator(1)],
        help_text="Target audience for the book"
    )
    isbn = models.CharField(
        max_length=13,
        validators=[validate_isbn],
        unique=True,
        help_text="ISBN-10 or ISBN-13 (digits only)"
    )
    year_of_publication = models.PositiveIntegerField(
        validators=[validate_year],
        help_text="Year the book was published"
    )
    publisher = models.CharField(
        max_length=100,
        validators=[MinLengthValidator(1)],
        help_text="Publisher name"
    )
    overview = models.TextField(
        validators=[MinLengthValidator(10)],
        help_text="Book overview/description (minimum 10 characters)"
    )
    category = models.CharField(
        max_length=50,
        validators=[MinLengthValidator(1)],
        help_text="Book category/genre"
    )
    cover_image = models.ImageField(
        upload_to='cover_images/',
        blank=True,
        null=True,
        help_text="Book cover image (optional)"
    )
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Publication status of the book"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['isbn']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.call_number} - {self.status}"

    def clean(self):
        """Additional model validation"""
        super().clean()

        # Validate ISBN format
        if self.isbn:
            self.isbn = validate_isbn(self.isbn)

        # Validate year
        if self.year_of_publication:
            validate_year(self.year_of_publication)

    def save(self, *args, **kwargs):
        """Override save to ensure validation"""
        self.full_clean()
        super().save(*args, **kwargs)
