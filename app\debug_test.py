#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

from api.v1.models import BookBase
from api.v1.serializers import BookCreateSerializer

# Test data
test_data = {
    'call_number': 'TEST123',
    'material': 'Book',
    'audience': 'Children',
    'isbn': '1234567890',
    'year_of_publication': 2020,
    'publisher': 'Test Publisher',
    'overview': 'Test overview for testing purposes',
    'category': 'Test',
    'status': 'draft'
}

print("Testing serializer...")
serializer = BookCreateSerializer(data=test_data)
if serializer.is_valid():
    print("Serializer is valid")
    try:
        book = serializer.save()
        print(f"Book created successfully: {book.id}")
    except Exception as e:
        print(f"Error saving book: {e}")
else:
    print(f"Serializer errors: {serializer.errors}")

print("\nTesting model directly...")
try:
    book = BookBase.objects.create(**test_data)
    print(f"Model created successfully: {book.id}")
except Exception as e:
    print(f"Error creating model: {e}")
