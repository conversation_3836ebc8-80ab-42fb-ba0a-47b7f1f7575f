# Generated by Django 5.2.3 on 2025-06-29 11:21

import api.v1.models
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('v1', '0002_bookbase_status'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bookbase',
            options={'ordering': ['-created_at']},
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='audience',
            field=models.CharField(help_text='Target audience for the book', max_length=50, validators=[django.core.validators.MinLengthValidator(1)]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='call_number',
            field=models.CharField(help_text='Library call number for the book', max_length=50, validators=[django.core.validators.MinLengthValidator(1)]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='category',
            field=models.Char<PERSON>ield(help_text='Book category/genre', max_length=50, validators=[django.core.validators.MinLengthValidator(1)]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='cover_image',
            field=models.ImageField(blank=True, help_text='Book cover image (optional)', null=True, upload_to='cover_images/'),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='isbn',
            field=models.CharField(help_text='ISBN-10 or ISBN-13 (digits only)', max_length=13, unique=True, validators=[api.v1.models.validate_isbn]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='material',
            field=models.CharField(help_text='Type of material (e.g., book, journal, etc.)', max_length=50, validators=[django.core.validators.MinLengthValidator(1)]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='overview',
            field=models.TextField(help_text='Book overview/description (minimum 10 characters)', validators=[django.core.validators.MinLengthValidator(10)]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='publisher',
            field=models.CharField(help_text='Publisher name', max_length=100, validators=[django.core.validators.MinLengthValidator(1)]),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('published', 'Published')], default='draft', help_text='Publication status of the book', max_length=10),
        ),
        migrations.AlterField(
            model_name='bookbase',
            name='year_of_publication',
            field=models.PositiveIntegerField(help_text='Year the book was published', validators=[api.v1.models.validate_year]),
        ),
        migrations.AddIndex(
            model_name='bookbase',
            index=models.Index(fields=['status'], name='v1_bookbase_status_3dc822_idx'),
        ),
        migrations.AddIndex(
            model_name='bookbase',
            index=models.Index(fields=['isbn'], name='v1_bookbase_isbn_c55dea_idx'),
        ),
        migrations.AddIndex(
            model_name='bookbase',
            index=models.Index(fields=['created_at'], name='v1_bookbase_created_81149f_idx'),
        ),
    ]
