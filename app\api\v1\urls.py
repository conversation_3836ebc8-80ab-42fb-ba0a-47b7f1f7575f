from django.urls import path
from .views import *

urlpatterns = [
    # Existing endpoints
    path('get-pdf/', GetPDFView.as_view(), name='get-pdf'),
    path('get-audio/', GetAudioView.as_view(), name='get-audio'),
    path('upload_zip_file/', ImagesToPDF.as_view(), name='upload-zipfile'),

    # Book management endpoints
    path('books/', BookCreateView.as_view(), name='book-create'),  # POST for creating books
    path('books/list/', BookListView.as_view(), name='book-list'),  # GET for listing books
    path('books/<uuid:book_id>/', BookDetailView.as_view(), name='book-detail'),  # GET, PUT, PATCH, DELETE for individual books
]
