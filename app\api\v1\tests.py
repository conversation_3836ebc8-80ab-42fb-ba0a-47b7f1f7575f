from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io
import json
import uuid
from .models import BookBase
from .serializers import BookCreateSerializer, BookListSerializer


class BookModelTestCase(TestCase):
    """Test cases for BookBase model"""

    def setUp(self):
        self.valid_book_data = {
            'call_number': 'PZ7.R79835 Har 2009',
            'material': 'Book',
            'audience': 'Children',
            'isbn': '9780545010221',
            'year_of_publication': 2009,
            'publisher': 'Scholastic',
            'overview': '<PERSON> returns for another year at Hogwarts, facing new adventures and magical challenges.',
            'category': 'Fantasy',
            'status': 'draft'
        }

    def test_create_valid_book(self):
        """Test creating a book with valid data"""
        book = BookBase.objects.create(**self.valid_book_data)
        self.assertEqual(book.call_number, 'PZ7.R79835 Har 2009')
        self.assertEqual(book.status, 'draft')
        self.assertIsNotNone(book.id)
        self.assertIsNotNone(book.created_at)
        self.assertIsNotNone(book.updated_at)

    def test_isbn_validation(self):
        """Test ISBN validation"""
        # Test invalid ISBN length
        invalid_data = self.valid_book_data.copy()
        invalid_data['isbn'] = '123'

        with self.assertRaises(Exception):
            book = BookBase(**invalid_data)
            book.full_clean()

    def test_unique_isbn_constraint(self):
        """Test that ISBN must be unique"""
        BookBase.objects.create(**self.valid_book_data)

        # Try to create another book with same ISBN
        with self.assertRaises(Exception):
            duplicate_data = self.valid_book_data.copy()
            duplicate_data['call_number'] = 'Different Call Number'
            BookBase.objects.create(**duplicate_data)

    def test_year_validation(self):
        """Test year validation"""
        invalid_data = self.valid_book_data.copy()
        invalid_data['year_of_publication'] = 3000  # Future year

        with self.assertRaises(Exception):
            book = BookBase(**invalid_data)
            book.full_clean()

    def test_status_choices(self):
        """Test status field choices"""
        book = BookBase.objects.create(**self.valid_book_data)

        # Test valid statuses
        book.status = 'published'
        book.save()
        self.assertEqual(book.status, 'published')

        book.status = 'draft'
        book.save()
        self.assertEqual(book.status, 'draft')


class BookSerializerTestCase(TestCase):
    """Test cases for Book serializers"""

    def setUp(self):
        self.valid_book_data = {
            'call_number': 'PZ7.R79835 Har 2009',
            'material': 'Book',
            'audience': 'Children',
            'isbn': '9780545010221',
            'year_of_publication': 2009,
            'publisher': 'Scholastic',
            'overview': 'Harry Potter returns for another year at Hogwarts, facing new adventures and magical challenges.',
            'category': 'Fantasy',
            'status': 'draft'
        }

    def test_valid_serializer(self):
        """Test serializer with valid data"""
        serializer = BookCreateSerializer(data=self.valid_book_data)
        self.assertTrue(serializer.is_valid())

    def test_missing_required_fields(self):
        """Test serializer with missing required fields"""
        incomplete_data = {
            'call_number': 'PZ7.R79835 Har 2009',
            'material': 'Book'
            # Missing other required fields
        }
        serializer = BookCreateSerializer(data=incomplete_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('audience', serializer.errors)
        self.assertIn('isbn', serializer.errors)

    def test_invalid_isbn_format(self):
        """Test serializer with invalid ISBN"""
        invalid_data = self.valid_book_data.copy()
        invalid_data['isbn'] = 'invalid-isbn'

        serializer = BookCreateSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('isbn', serializer.errors)

    def test_invalid_status(self):
        """Test serializer with invalid status"""
        invalid_data = self.valid_book_data.copy()
        invalid_data['status'] = 'invalid_status'

        serializer = BookCreateSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('status', serializer.errors)

    def test_whitespace_validation(self):
        """Test that fields cannot be just whitespace"""
        invalid_data = self.valid_book_data.copy()
        invalid_data['call_number'] = '   '  # Just whitespace

        serializer = BookCreateSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('call_number', serializer.errors)


class BookAPITestCase(APITestCase):
    """Test cases for Book API endpoints"""

    def setUp(self):
        self.valid_book_data = {
            'call_number': 'PZ7.R79835 Har 2009',
            'material': 'Book',
            'audience': 'Children',
            'isbn': '9780545010221',
            'year_of_publication': 2009,
            'publisher': 'Scholastic',
            'overview': 'Harry Potter returns for another year at Hogwarts, facing new adventures and magical challenges.',
            'category': 'Fantasy',
            'status': 'draft'
        }

        self.create_url = reverse('book-create')
        self.list_url = reverse('book-list')

    def test_create_book_success(self):
        """Test successful book creation"""
        response = self.client.post(self.create_url, self.valid_book_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['isbn'], '9780545010221')
        self.assertEqual(response.data['data']['status'], 'draft')

    def test_create_book_invalid_data(self):
        """Test book creation with invalid data"""
        invalid_data = self.valid_book_data.copy()
        del invalid_data['isbn']  # Remove required field

        response = self.client.post(self.create_url, invalid_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('errors', response.data)
        self.assertIn('isbn', response.data['errors'])

    def test_create_duplicate_isbn(self):
        """Test creating book with duplicate ISBN"""
        # Create first book
        BookBase.objects.create(**self.valid_book_data)

        # Try to create duplicate
        response = self.client.post(self.create_url, self.valid_book_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('isbn', response.data['errors'])

    def test_list_books_empty(self):
        """Test listing books when none exist"""
        response = self.client.get(self.list_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['count'], 0)
        self.assertEqual(len(response.data['data']['books']), 0)

    def test_list_books_with_data(self):
        """Test listing books when data exists"""
        # Create test books
        book1 = BookBase.objects.create(**self.valid_book_data)

        book2_data = self.valid_book_data.copy()
        book2_data['isbn'] = '9780545010222'
        book2_data['status'] = 'published'
        book2 = BookBase.objects.create(**book2_data)

        response = self.client.get(self.list_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['count'], 2)
        self.assertEqual(len(response.data['data']['books']), 2)

    def test_list_books_filter_by_status(self):
        """Test filtering books by status"""
        # Create test books
        book1 = BookBase.objects.create(**self.valid_book_data)

        book2_data = self.valid_book_data.copy()
        book2_data['isbn'] = '9780545010222'
        book2_data['status'] = 'published'
        book2 = BookBase.objects.create(**book2_data)

        # Filter by draft status
        response = self.client.get(self.list_url, {'status': 'draft'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['count'], 1)
        self.assertEqual(response.data['data']['books'][0]['status'], 'draft')

        # Filter by published status
        response = self.client.get(self.list_url, {'status': 'published'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['count'], 1)
        self.assertEqual(response.data['data']['books'][0]['status'], 'published')

    def test_list_books_invalid_status_filter(self):
        """Test filtering with invalid status"""
        response = self.client.get(self.list_url, {'status': 'invalid_status'})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('status', response.data['errors'])

    def test_get_book_detail_success(self):
        """Test getting individual book details"""
        book = BookBase.objects.create(**self.valid_book_data)
        detail_url = reverse('book-detail', kwargs={'book_id': book.id})

        response = self.client.get(detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['id'], str(book.id))
        self.assertEqual(response.data['data']['isbn'], book.isbn)

    def test_get_book_detail_not_found(self):
        """Test getting non-existent book"""
        non_existent_id = uuid.uuid4()
        detail_url = reverse('book-detail', kwargs={'book_id': non_existent_id})

        response = self.client.get(detail_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn('not_found', response.data['errors'])

    def test_update_book_success(self):
        """Test updating a book"""
        book = BookBase.objects.create(**self.valid_book_data)
        detail_url = reverse('book-detail', kwargs={'book_id': book.id})

        update_data = self.valid_book_data.copy()
        update_data['status'] = 'published'
        update_data['overview'] = 'Updated overview'

        response = self.client.put(detail_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['status'], 'published')
        self.assertEqual(response.data['data']['overview'], 'Updated overview')

    def test_partial_update_book_success(self):
        """Test partially updating a book"""
        book = BookBase.objects.create(**self.valid_book_data)
        detail_url = reverse('book-detail', kwargs={'book_id': book.id})

        update_data = {'status': 'published'}

        response = self.client.patch(detail_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['status'], 'published')
        # Other fields should remain unchanged
        self.assertEqual(response.data['data']['overview'], book.overview)

    def test_delete_book_success(self):
        """Test deleting a book"""
        book = BookBase.objects.create(**self.valid_book_data)
        detail_url = reverse('book-detail', kwargs={'book_id': book.id})

        response = self.client.delete(detail_url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(BookBase.objects.filter(id=book.id).exists())

    def test_delete_book_not_found(self):
        """Test deleting non-existent book"""
        non_existent_id = uuid.uuid4()
        detail_url = reverse('book-detail', kwargs={'book_id': non_existent_id})

        response = self.client.delete(detail_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
