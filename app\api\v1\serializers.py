from rest_framework import serializers
from .models import BookBase
from django.core.exceptions import ValidationError as DjangoValidationError


class FileCheckSerializer(serializers.Serializer):
    isbn_number = serializers.CharField(
        help_text="Provide a valid ISBN-10 (digits only).",
        required=True,
        min_length=10
    )


class FileUploadSerializer(serializers.Serializer):
    file = serializers.FileField(help_text="Please upload the zip file", required=True)


class BookCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating books"""

    class Meta:
        model = BookBase
        fields = [
            'id', 'call_number', 'material', 'audience', 'isbn',
            'year_of_publication', 'publisher', 'overview', 'category',
            'cover_image', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        extra_kwargs = {
            'call_number': {'required': True},
            'material': {'required': True},
            'audience': {'required': True},
            'isbn': {'required': True},
            'year_of_publication': {'required': True},
            'publisher': {'required': True},
            'overview': {'required': True},
            'category': {'required': True},
            'status': {'required': False, 'default': 'draft'},
        }

    def validate_isbn(self, value):
        """Custom ISBN validation"""
        from .models import validate_isbn
        try:
            cleaned_isbn = validate_isbn(value)

            # Check for uniqueness (excluding current instance if updating)
            queryset = BookBase.objects.filter(isbn=cleaned_isbn)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)

            if queryset.exists():
                raise serializers.ValidationError("A book with this ISBN already exists.")

            return cleaned_isbn
        except DjangoValidationError as e:
            raise serializers.ValidationError(str(e))

    def validate_year_of_publication(self, value):
        """Custom year validation"""
        from .models import validate_year
        try:
            validate_year(value)
            return value
        except DjangoValidationError as e:
            raise serializers.ValidationError(str(e))

    def validate_status(self, value):
        """Validate status field"""
        valid_statuses = [choice[0] for choice in BookBase.STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(
                f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
            )
        return value

    def validate(self, attrs):
        """Cross-field validation"""
        # Ensure all required text fields are not just whitespace
        text_fields = ['call_number', 'material', 'audience', 'publisher', 'overview', 'category']
        for field in text_fields:
            if field in attrs and not attrs[field].strip():
                raise serializers.ValidationError({
                    field: "This field cannot be empty or contain only whitespace."
                })

        return attrs


class BookListSerializer(serializers.ModelSerializer):
    """Serializer for listing books (read-only)"""

    class Meta:
        model = BookBase
        fields = [
            'id', 'call_number', 'material', 'audience', 'isbn',
            'year_of_publication', 'publisher', 'overview', 'category',
            'cover_image', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = '__all__'


class BookStatusUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating only the status of a book"""

    class Meta:
        model = BookBase
        fields = ['status']

    def validate_status(self, value):
        """Validate status field"""
        valid_statuses = [choice[0] for choice in BookBase.STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(
                f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
            )
        return value